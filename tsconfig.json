{"extends": "expo/tsconfig.base", "compilerOptions": {"strict": true, "baseUrl": ".", "paths": {"@/*": ["./src/*"], "@assets/*": ["./assets/*"], "@components/*": ["./src/components/*"], "@screens/*": ["./src/screens/*"], "@hooks/*": ["./src/hooks/*"], "@utils/*": ["./src/utils/*"], "@types/*": ["./src/types/*"]}, "jsx": "react-native", "esModuleInterop": true, "allowSyntheticDefaultImports": true, "forceConsistentCasingInFileNames": true, "skipLibCheck": true}, "include": ["**/*.ts", "**/*.tsx", ".expo/types/**/*.ts", "expo-env.d.ts", "nativewind-env.d.ts", "src/types/**/*.d.ts", "types/**/*.d.ts"], "exclude": ["node_modules"]}